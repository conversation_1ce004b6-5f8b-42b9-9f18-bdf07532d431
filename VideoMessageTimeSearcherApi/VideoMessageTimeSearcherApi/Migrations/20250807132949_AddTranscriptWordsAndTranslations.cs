﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace VideoMessageTimeSearcherApi.Migrations
{
    /// <inheritdoc />
    public partial class AddTranscriptWordsAndTranslations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TranscriptTranslations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    VideoId = table.Column<int>(type: "integer", nullable: false),
                    LanguageCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    LanguageName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    TranslatedTranscript = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranscriptTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranscriptTranslations_Videos_VideoId",
                        column: x => x.VideoId,
                        principalTable: "Videos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TranscriptWords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TranscriptSegmentId = table.Column<int>(type: "integer", nullable: false),
                    WordTimingsJson = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranscriptWords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranscriptWords_TranscriptSegments_TranscriptSegmentId",
                        column: x => x.TranscriptSegmentId,
                        principalTable: "TranscriptSegments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TranscriptTranslationWords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TranscriptTranslationId = table.Column<int>(type: "integer", nullable: false),
                    OriginalTranscriptSegmentId = table.Column<int>(type: "integer", nullable: false),
                    TranslatedWordTimingsJson = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranscriptTranslationWords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranscriptTranslationWords_TranscriptSegments_OriginalTrans~",
                        column: x => x.OriginalTranscriptSegmentId,
                        principalTable: "TranscriptSegments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TranscriptTranslationWords_TranscriptTranslations_Transcrip~",
                        column: x => x.TranscriptTranslationId,
                        principalTable: "TranscriptTranslations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TranscriptTranslations_VideoId_LanguageCode",
                table: "TranscriptTranslations",
                columns: new[] { "VideoId", "LanguageCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TranscriptTranslationWords_OriginalTranscriptSegmentId",
                table: "TranscriptTranslationWords",
                column: "OriginalTranscriptSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_TranscriptTranslationWords_TranscriptTranslationId_Original~",
                table: "TranscriptTranslationWords",
                columns: new[] { "TranscriptTranslationId", "OriginalTranscriptSegmentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TranscriptWords_TranscriptSegmentId",
                table: "TranscriptWords",
                column: "TranscriptSegmentId",
                unique: true);

            // Migrate existing WordTiming data to JSON format in TranscriptWords
            // Only migrate if WordTimings table has data
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM ""WordTimings"" LIMIT 1) THEN
                        INSERT INTO ""TranscriptWords"" (""TranscriptSegmentId"", ""WordTimingsJson"", ""CreatedAt"", ""UpdatedAt"")
                        SELECT
                            ""SegmentId"",
                            jsonb_agg(
                                jsonb_build_object(
                                    'word', ""Word"",
                                    'startTime', ""StartTime"",
                                    'endTime', ""EndTime"",
                                    'wordIndex', ""WordIndex""
                                ) ORDER BY ""WordIndex""
                            ) as ""WordTimingsJson"",
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP
                        FROM ""WordTimings""
                        GROUP BY ""SegmentId""
                        ON CONFLICT (""TranscriptSegmentId"") DO NOTHING;
                    END IF;
                END $$;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TranscriptTranslationWords");

            migrationBuilder.DropTable(
                name: "TranscriptWords");

            migrationBuilder.DropTable(
                name: "TranscriptTranslations");
        }
    }
}
