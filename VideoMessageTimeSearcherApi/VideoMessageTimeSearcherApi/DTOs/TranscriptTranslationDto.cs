namespace VideoMessageTimeSearcherApi.DTOs
{
    public class TranscriptTranslationDto
    {
        public int Id { get; set; }
        public int VideoId { get; set; }
        public string LanguageCode { get; set; } = string.Empty;
        public string LanguageName { get; set; } = string.Empty;
        public string TranslatedTranscript { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public List<TranscriptTranslationWordsDto> TranslationWords { get; set; } = new List<TranscriptTranslationWordsDto>();
    }
}
