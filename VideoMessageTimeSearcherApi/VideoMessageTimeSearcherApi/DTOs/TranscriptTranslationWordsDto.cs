namespace VideoMessageTimeSearcherApi.DTOs
{
    /// <summary>
    /// DTO for translated word timing within JSON array
    /// </summary>
    public class TranslatedWordTimingJsonDto
    {
        public string Word { get; set; } = string.Empty; // Translated word
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public int WordIndex { get; set; }
        public string OriginalWord { get; set; } = string.Empty; // Original word for reference
    }

    public class TranscriptTranslationWordsDto
    {
        public int Id { get; set; }
        public int TranscriptTranslationId { get; set; }
        public int OriginalTranscriptSegmentId { get; set; }
        public List<TranslatedWordTimingJsonDto> TranslatedWordTimings { get; set; } = new List<TranslatedWordTimingJsonDto>();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
