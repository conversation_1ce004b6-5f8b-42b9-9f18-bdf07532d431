using System.ComponentModel.DataAnnotations;

namespace VideoMessageTimeSearcherApi.DTOs
{
    public class CreateTranscriptTranslationRequest
    {
        [Required]
        public int VideoId { get; set; }
        
        [Required]
        [StringLength(10)]
        public string LanguageCode { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string LanguageName { get; set; } = string.Empty;
        
        public string TranslatedTranscript { get; set; } = string.Empty;
        
        public List<CreateTranscriptTranslationWordsRequest> TranslationWords { get; set; } = new List<CreateTranscriptTranslationWordsRequest>();
    }

    public class CreateTranscriptTranslationWordsRequest
    {
        [Required]
        public int OriginalTranscriptSegmentId { get; set; }
        
        public List<CreateTranslatedWordTimingRequest> TranslatedWordTimings { get; set; } = new List<CreateTranslatedWordTimingRequest>();
    }

    public class CreateTranslatedWordTimingRequest
    {
        [Required]
        public string Word { get; set; } = string.Empty;
        
        [Required]
        public double StartTime { get; set; }
        
        [Required]
        public double EndTime { get; set; }
        
        [Required]
        public int WordIndex { get; set; }
        
        public string OriginalWord { get; set; } = string.Empty;
    }
}
