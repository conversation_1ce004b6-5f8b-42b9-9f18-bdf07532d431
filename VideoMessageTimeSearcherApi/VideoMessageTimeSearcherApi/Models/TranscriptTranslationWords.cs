using System.ComponentModel.DataAnnotations.Schema;

namespace VideoMessageTimeSearcherApi.Models
{
    public class TranscriptTranslationWords
    {
        public int Id { get; set; }
        public int TranscriptTranslationId { get; set; }
        public int OriginalTranscriptSegmentId { get; set; } // Maps to the original segment
        
        /// <summary>
        /// JSON string containing array of translated word timing objects
        /// Format: [{"word": "hola", "startTime": 1.5, "endTime": 2.0, "wordIndex": 0, "originalWord": "hello"}, ...]
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string TranslatedWordTimingsJson { get; set; } = "[]";
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public TranscriptTranslation TranscriptTranslation { get; set; }
        public TranscriptSegment OriginalTranscriptSegment { get; set; }
    }
}
