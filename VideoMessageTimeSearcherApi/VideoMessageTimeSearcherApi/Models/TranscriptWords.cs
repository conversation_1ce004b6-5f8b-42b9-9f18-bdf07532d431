using System.ComponentModel.DataAnnotations.Schema;

namespace VideoMessageTimeSearcherApi.Models
{
    public class TranscriptWords
    {
        public int Id { get; set; }
        public int TranscriptSegmentId { get; set; }
        
        /// <summary>
        /// JSON string containing array of word timing objects
        /// Format: [{"word": "hello", "startTime": 1.5, "endTime": 2.0, "wordIndex": 0}, ...]
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string WordTimingsJson { get; set; } = "[]";
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public TranscriptSegment TranscriptSegment { get; set; }
    }
}
