namespace VideoMessageTimeSearcherApi.Models
{
    public class TranscriptTranslation
    {
        public int Id { get; set; }
        public int VideoId { get; set; }
        public string LanguageCode { get; set; } = string.Empty; // e.g., "es", "fr", "de"
        public string LanguageName { get; set; } = string.Empty; // e.g., "Spanish", "French", "German"
        public string TranslatedTranscript { get; set; } = string.Empty; // Full translated transcript for searching
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public Video Video { get; set; }
        public ICollection<TranscriptTranslationWords> TranscriptTranslationWords { get; set; } = new List<TranscriptTranslationWords>();
    }
}
