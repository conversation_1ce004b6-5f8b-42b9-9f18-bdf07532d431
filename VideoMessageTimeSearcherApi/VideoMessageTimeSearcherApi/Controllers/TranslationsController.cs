using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TranslationsController : ControllerBase
    {
        private readonly ITranslationService _translationService;

        public TranslationsController(ITranslationService translationService)
        {
            _translationService = translationService;
        }

        /// <summary>
        /// Get all translations for a specific video
        /// </summary>
        [HttpGet("video/{videoId}")]
        public async Task<ActionResult<List<TranscriptTranslationDto>>> GetVideoTranslations(int videoId)
        {
            try
            {
                var translations = await _translationService.GetVideoTranslationsAsync(videoId);
                return Ok(translations);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving translations", error = ex.Message });
            }
        }

        /// <summary>
        /// Get a specific translation by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<TranscriptTranslationDto>> GetTranslation(int id)
        {
            try
            {
                var translation = await _translationService.GetTranslationByIdAsync(id);
                if (translation == null)
                {
                    return NotFound(new { message = "Translation not found" });
                }
                return Ok(translation);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving the translation", error = ex.Message });
            }
        }

        /// <summary>
        /// Create a new translation for a video
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<TranscriptTranslationDto>> CreateTranslation([FromBody] CreateTranscriptTranslationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var translation = await _translationService.CreateTranslationAsync(request);
                return CreatedAtAction(nameof(GetTranslation), new { id = translation.Id }, translation);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while creating the translation", error = ex.Message });
            }
        }

        /// <summary>
        /// Update an existing translation
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult> UpdateTranslation(int id, [FromBody] CreateTranscriptTranslationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _translationService.UpdateTranslationAsync(id, request);
                if (!success)
                {
                    return NotFound(new { message = "Translation not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while updating the translation", error = ex.Message });
            }
        }

        /// <summary>
        /// Delete a translation
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteTranslation(int id)
        {
            try
            {
                var success = await _translationService.DeleteTranslationAsync(id);
                if (!success)
                {
                    return NotFound(new { message = "Translation not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the translation", error = ex.Message });
            }
        }
    }
}
