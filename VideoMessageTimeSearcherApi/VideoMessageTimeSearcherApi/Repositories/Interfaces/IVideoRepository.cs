using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;

namespace VideoMessageTimeSearcherApi.Repositories.Interfaces
{
    public interface IVideoRepository
    {
        // Search operations
        Task<PagedResult<Video>> SearchVideosAsync(SearchVideosRequest request);
        Task<List<WordOccurrenceDto>> SearchTranscriptsAsync(SearchTranscriptRequest request);
        
        // Video operations
        Task<Video> GetVideoByIdAsync(int id);
        Task<Video> CreateVideoAsync(Video video);
        Task<bool> UpdateVideoAsync(Video video);
        Task<bool> DeleteVideoAsync(int id);
        Task<List<TranscriptSegmentGroup>> GetVideoTranscriptSegmentsAsync(int videoId);
        
        // Segment group operations
        Task<TranscriptSegmentGroup> GetSegmentGroupByIdAsync(int id);
        Task<TranscriptSegmentGroup> CreateSegmentGroupAsync(TranscriptSegmentGroup segmentGroup);
        Task<bool> DeleteSegmentGroupAsync(int id);
        
        // Segment operations
        Task<TranscriptSegment> GetSegmentByIdAsync(int id);
        Task<TranscriptSegment> CreateSegmentAsync(TranscriptSegment segment);
        Task<bool> DeleteSegmentAsync(int id);
        
        // Word timing operations
        Task<WordTiming> GetWordTimingByIdAsync(int id);
        Task<WordTiming> CreateWordTimingAsync(WordTiming wordTiming);
        Task<bool> DeleteWordTimingAsync(int id);

        // Transcript words operations
        Task<TranscriptWords> GetTranscriptWordsBySegmentIdAsync(int segmentId);
        Task<TranscriptWords> CreateTranscriptWordsAsync(TranscriptWords transcriptWords);
        Task<bool> UpdateTranscriptWordsAsync(TranscriptWords transcriptWords);
        Task<bool> DeleteTranscriptWordsAsync(int id);

        // Translation operations
        Task<List<TranscriptTranslation>> GetVideoTranslationsAsync(int videoId);
        Task<TranscriptTranslation> GetTranslationByIdAsync(int id);
        Task<TranscriptTranslation> CreateTranslationAsync(TranscriptTranslation translation);
        Task<bool> UpdateTranslationAsync(TranscriptTranslation translation);
        Task<bool> DeleteTranslationAsync(int id);

        // Translation words operations
        Task<List<TranscriptTranslationWords>> GetTranslationWordsAsync(int translationId);
        Task<TranscriptTranslationWords> CreateTranslationWordsAsync(TranscriptTranslationWords translationWords);
        Task<bool> UpdateTranslationWordsAsync(TranscriptTranslationWords translationWords);
        Task<bool> DeleteTranslationWordsAsync(int id);
    }
}
