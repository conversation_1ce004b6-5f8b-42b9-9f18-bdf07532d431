using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Models.Identity;

namespace VideoMessageTimeSearcherApi.DbContexts
{
    public class AppDbContext : IdentityDbContext<User, IdentityRole, string>
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Video> Videos { get; set; }
        public DbSet<TranscriptSegmentGroup> TranscriptSegmentGroups { get; set; }
        public DbSet<TranscriptSegment> TranscriptSegments { get; set; }
        public DbSet<WordTiming> WordTimings { get; set; }
        public DbSet<TranscriptWords> TranscriptWords { get; set; }
        public DbSet<TranscriptTranslation> TranscriptTranslations { get; set; }
        public DbSet<TranscriptTranslationWords> TranscriptTranslationWords { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Rename the Identity tables to use singular names
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                var tableName = entityType.GetTableName();
                if (tableName != null && tableName.StartsWith("AspNet"))
                {
                    entityType.SetTableName(tableName[6..]);
                }
            }

            // Configure Video entity
            modelBuilder.Entity<Video>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired();
                entity.Property(e => e.Description).IsRequired(false);
                entity.Property(e => e.DurationSeconds).IsRequired();
                entity.Property(e => e.Transcript).IsRequired(false);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.FilePath).IsRequired();

                entity.HasMany(v => v.TranscriptTranslations)
                      .WithOne(tt => tt.Video)
                      .HasForeignKey(tt => tt.VideoId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure TranscriptSegmentGroup entity
            modelBuilder.Entity<TranscriptSegmentGroup>(entity =>
            {
                entity.ToTable(tb => 
                    tb.HasCheckConstraint("CK_TranscriptSegmentGroups_TimeRange", 
                        "\"StartTime\" >= 0 AND \"EndTime\" >= \"StartTime\""));

                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Video)
                    .WithMany(v => v.TranscriptSegmentGroups)
                    .HasForeignKey(e => e.VideoId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.StartTime).IsRequired();
                entity.Property(e => e.EndTime).IsRequired();
            });

            // Configure TranscriptSegment entity
            modelBuilder.Entity<TranscriptSegment>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(e => e.Group)
                    .WithMany(g => g.TranscriptSegments)
                    .HasForeignKey(e => e.GroupId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(s => s.TranscriptWords)
                      .WithOne(tw => tw.TranscriptSegment)
                      .HasForeignKey<TranscriptWords>(tw => tw.TranscriptSegmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(s => s.TranscriptTranslationWords)
                      .WithOne(ttw => ttw.OriginalTranscriptSegment)
                      .HasForeignKey(ttw => ttw.OriginalTranscriptSegmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.LanguageCode).IsRequired().HasMaxLength(10);
                entity.Property(e => e.Text).IsRequired();

                entity.HasIndex(e => new { e.GroupId, e.LanguageCode })
                    .IsUnique();
            });

            // Configure WordTiming entity
            modelBuilder.Entity<WordTiming>(entity =>
            {
                entity.ToTable(tb =>
                    tb.HasCheckConstraint("CK_WordTimings_TimeRange",
                        "\"StartTime\" >= 0 AND \"EndTime\" >= \"StartTime\""));

                entity.HasKey(e => e.Id);

                entity.HasOne(w => w.Segment)
                      .WithMany(s => s.WordTimings)
                      .HasForeignKey(w => w.SegmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.Word).IsRequired();
                entity.Property(e => e.StartTime).IsRequired();
                entity.Property(e => e.EndTime).IsRequired();
                entity.Property(e => e.WordIndex).IsRequired();
            });

            // Configure TranscriptWords entity
            modelBuilder.Entity<TranscriptWords>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(tw => tw.TranscriptSegment)
                      .WithOne(s => s.TranscriptWords)
                      .HasForeignKey<TranscriptWords>(tw => tw.TranscriptSegmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.WordTimingsJson)
                      .IsRequired()
                      .HasColumnType("jsonb")
                      .HasDefaultValue("[]");

                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            });

            // Configure TranscriptTranslation entity
            modelBuilder.Entity<TranscriptTranslation>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(tt => tt.Video)
                      .WithMany(v => v.TranscriptTranslations)
                      .HasForeignKey(tt => tt.VideoId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.LanguageCode).IsRequired().HasMaxLength(10);
                entity.Property(e => e.LanguageName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.TranslatedTranscript).IsRequired(false);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasIndex(e => new { e.VideoId, e.LanguageCode })
                      .IsUnique();
            });

            // Configure TranscriptTranslationWords entity
            modelBuilder.Entity<TranscriptTranslationWords>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasOne(ttw => ttw.TranscriptTranslation)
                      .WithMany(tt => tt.TranscriptTranslationWords)
                      .HasForeignKey(ttw => ttw.TranscriptTranslationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ttw => ttw.OriginalTranscriptSegment)
                      .WithMany(s => s.TranscriptTranslationWords)
                      .HasForeignKey(ttw => ttw.OriginalTranscriptSegmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.TranslatedWordTimingsJson)
                      .IsRequired()
                      .HasColumnType("jsonb")
                      .HasDefaultValue("[]");

                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasIndex(e => new { e.TranscriptTranslationId, e.OriginalTranscriptSegmentId })
                      .IsUnique();
            });

        }
    }
}
