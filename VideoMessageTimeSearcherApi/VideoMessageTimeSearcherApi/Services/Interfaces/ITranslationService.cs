using VideoMessageTimeSearcherApi.DTOs;

namespace VideoMessageTimeSearcherApi.Services.Interfaces
{
    public interface ITranslationService
    {
        Task<List<TranscriptTranslationDto>> GetVideoTranslationsAsync(int videoId);
        Task<TranscriptTranslationDto> GetTranslationByIdAsync(int id);
        Task<TranscriptTranslationDto> CreateTranslationAsync(CreateTranscriptTranslationRequest request);
        Task<bool> UpdateTranslationAsync(int id, CreateTranscriptTranslationRequest request);
        Task<bool> DeleteTranslationAsync(int id);
    }
}
