using AutoMapper;
using System.Text.Json;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Services
{
    public class TranslationService : ITranslationService
    {
        private readonly IVideoRepository _videoRepository;
        private readonly IMapper _mapper;

        public TranslationService(IVideoRepository videoRepository, IMapper mapper)
        {
            _videoRepository = videoRepository;
            _mapper = mapper;
        }

        public async Task<List<TranscriptTranslationDto>> GetVideoTranslationsAsync(int videoId)
        {
            var translations = await _videoRepository.GetVideoTranslationsAsync(videoId);
            var result = new List<TranscriptTranslationDto>();

            foreach (var translation in translations)
            {
                var dto = _mapper.Map<TranscriptTranslationDto>(translation);
                
                // Map translation words
                dto.TranslationWords = translation.TranscriptTranslationWords.Select(ttw => 
                {
                    var translationWordsDto = _mapper.Map<TranscriptTranslationWordsDto>(ttw);
                    
                    // Deserialize JSON word timings
                    if (!string.IsNullOrEmpty(ttw.TranslatedWordTimingsJson))
                    {
                        try
                        {
                            translationWordsDto.TranslatedWordTimings = 
                                JsonSerializer.Deserialize<List<TranslatedWordTimingJsonDto>>(ttw.TranslatedWordTimingsJson) 
                                ?? new List<TranslatedWordTimingJsonDto>();
                        }
                        catch (JsonException)
                        {
                            translationWordsDto.TranslatedWordTimings = new List<TranslatedWordTimingJsonDto>();
                        }
                    }
                    
                    return translationWordsDto;
                }).ToList();
                
                result.Add(dto);
            }

            return result;
        }

        public async Task<TranscriptTranslationDto> GetTranslationByIdAsync(int id)
        {
            var translation = await _videoRepository.GetTranslationByIdAsync(id);
            if (translation == null) return null;

            var dto = _mapper.Map<TranscriptTranslationDto>(translation);
            
            // Map translation words with JSON deserialization
            dto.TranslationWords = translation.TranscriptTranslationWords.Select(ttw => 
            {
                var translationWordsDto = _mapper.Map<TranscriptTranslationWordsDto>(ttw);
                
                if (!string.IsNullOrEmpty(ttw.TranslatedWordTimingsJson))
                {
                    try
                    {
                        translationWordsDto.TranslatedWordTimings = 
                            JsonSerializer.Deserialize<List<TranslatedWordTimingJsonDto>>(ttw.TranslatedWordTimingsJson) 
                            ?? new List<TranslatedWordTimingJsonDto>();
                    }
                    catch (JsonException)
                    {
                        translationWordsDto.TranslatedWordTimings = new List<TranslatedWordTimingJsonDto>();
                    }
                }
                
                return translationWordsDto;
            }).ToList();

            return dto;
        }

        public async Task<TranscriptTranslationDto> CreateTranslationAsync(CreateTranscriptTranslationRequest request)
        {
            // Create the translation entity
            var translation = new TranscriptTranslation
            {
                VideoId = request.VideoId,
                LanguageCode = request.LanguageCode,
                LanguageName = request.LanguageName,
                TranslatedTranscript = request.TranslatedTranscript
            };

            var createdTranslation = await _videoRepository.CreateTranslationAsync(translation);

            // Create translation words if provided
            if (request.TranslationWords != null && request.TranslationWords.Count > 0)
            {
                foreach (var translationWordsRequest in request.TranslationWords)
                {
                    var translationWords = new TranscriptTranslationWords
                    {
                        TranscriptTranslationId = createdTranslation.Id,
                        OriginalTranscriptSegmentId = translationWordsRequest.OriginalTranscriptSegmentId,
                        TranslatedWordTimingsJson = JsonSerializer.Serialize(translationWordsRequest.TranslatedWordTimings)
                    };

                    await _videoRepository.CreateTranslationWordsAsync(translationWords);
                }
            }

            return await GetTranslationByIdAsync(createdTranslation.Id);
        }

        public async Task<bool> UpdateTranslationAsync(int id, CreateTranscriptTranslationRequest request)
        {
            var translation = await _videoRepository.GetTranslationByIdAsync(id);
            if (translation == null) return false;

            // Update translation properties
            translation.LanguageCode = request.LanguageCode;
            translation.LanguageName = request.LanguageName;
            translation.TranslatedTranscript = request.TranslatedTranscript;

            var updateResult = await _videoRepository.UpdateTranslationAsync(translation);
            if (!updateResult) return false;

            // Update translation words
            // For simplicity, we'll delete existing and recreate
            foreach (var existingWords in translation.TranscriptTranslationWords.ToList())
            {
                await _videoRepository.DeleteTranslationWordsAsync(existingWords.Id);
            }

            // Create new translation words
            if (request.TranslationWords != null && request.TranslationWords.Count > 0)
            {
                foreach (var translationWordsRequest in request.TranslationWords)
                {
                    var translationWords = new TranscriptTranslationWords
                    {
                        TranscriptTranslationId = translation.Id,
                        OriginalTranscriptSegmentId = translationWordsRequest.OriginalTranscriptSegmentId,
                        TranslatedWordTimingsJson = JsonSerializer.Serialize(translationWordsRequest.TranslatedWordTimings)
                    };

                    await _videoRepository.CreateTranslationWordsAsync(translationWords);
                }
            }

            return true;
        }

        public async Task<bool> DeleteTranslationAsync(int id)
        {
            return await _videoRepository.DeleteTranslationAsync(id);
        }
    }
}
