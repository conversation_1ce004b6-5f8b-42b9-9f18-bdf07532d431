import apiClient from './apiClient';

export interface TranslatedWordTiming {
  word: string;
  startTime: number;
  endTime: number;
  wordIndex: number;
  originalWord: string;
}

export interface TranscriptTranslationWords {
  id: number;
  transcriptTranslationId: number;
  originalTranscriptSegmentId: number;
  translatedWordTimings: TranslatedWordTiming[];
  createdAt: string;
  updatedAt: string;
}

export interface TranscriptTranslation {
  id: number;
  videoId: number;
  languageCode: string;
  languageName: string;
  translatedTranscript: string;
  createdAt: string;
  updatedAt: string;
  translationWords: TranscriptTranslationWords[];
}

export interface CreateTranslatedWordTimingRequest {
  word: string;
  startTime: number;
  endTime: number;
  wordIndex: number;
  originalWord: string;
}

export interface CreateTranscriptTranslationWordsRequest {
  originalTranscriptSegmentId: number;
  translatedWordTimings: CreateTranslatedWordTimingRequest[];
}

export interface CreateTranscriptTranslationRequest {
  videoId: number;
  languageCode: string;
  languageName: string;
  translatedTranscript: string;
  translationWords: CreateTranscriptTranslationWordsRequest[];
}

// API functions
export const getVideoTranslations = async (videoId: number): Promise<TranscriptTranslation[]> => {
  const response = await apiClient.get(`/translations/video/${videoId}`);
  return response.data;
};

export const getTranslation = async (id: number): Promise<TranscriptTranslation> => {
  const response = await apiClient.get(`/translations/${id}`);
  return response.data;
};

export const createTranslation = async (request: CreateTranscriptTranslationRequest): Promise<TranscriptTranslation> => {
  const response = await apiClient.post('/translations', request);
  return response.data;
};

export const updateTranslation = async (id: number, request: CreateTranscriptTranslationRequest): Promise<void> => {
  await apiClient.put(`/translations/${id}`, request);
};

export const deleteTranslation = async (id: number): Promise<void> => {
  await apiClient.delete(`/translations/${id}`);
};
