import React, { useState, useEffect } from 'react';
import { FiPlus, FiEdit3, FiTrash2, FiGlobe } from 'react-icons/fi';
import { 
  TranscriptTranslation, 
  CreateTranscriptTranslationRequest,
  getVideoTranslations,
  createTranslation,
  updateTranslation,
  deleteTranslation
} from '../api/translationApi';

interface TranslationManagerProps {
  videoId: number;
  videoTitle: string;
}

const TranslationManager: React.FC<TranslationManagerProps> = ({ videoId, videoTitle }) => {
  const [translations, setTranslations] = useState<TranscriptTranslation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingTranslation, setEditingTranslation] = useState<TranscriptTranslation | null>(null);

  // Form state
  const [formData, setFormData] = useState<CreateTranscriptTranslationRequest>({
    videoId,
    languageCode: '',
    languageName: '',
    translatedTranscript: '',
    translationWords: []
  });

  useEffect(() => {
    loadTranslations();
  }, [videoId]);

  const loadTranslations = async () => {
    try {
      setLoading(true);
      const data = await getVideoTranslations(videoId);
      setTranslations(data);
      setError(null);
    } catch (err) {
      setError('Failed to load translations');
      console.error('Error loading translations:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTranslation = async () => {
    try {
      await createTranslation(formData);
      await loadTranslations();
      setShowCreateForm(false);
      resetForm();
    } catch (err) {
      setError('Failed to create translation');
      console.error('Error creating translation:', err);
    }
  };

  const handleUpdateTranslation = async () => {
    if (!editingTranslation) return;
    
    try {
      await updateTranslation(editingTranslation.id, formData);
      await loadTranslations();
      setEditingTranslation(null);
      resetForm();
    } catch (err) {
      setError('Failed to update translation');
      console.error('Error updating translation:', err);
    }
  };

  const handleDeleteTranslation = async (id: number) => {
    if (!confirm('Are you sure you want to delete this translation?')) return;
    
    try {
      await deleteTranslation(id);
      await loadTranslations();
    } catch (err) {
      setError('Failed to delete translation');
      console.error('Error deleting translation:', err);
    }
  };

  const resetForm = () => {
    setFormData({
      videoId,
      languageCode: '',
      languageName: '',
      translatedTranscript: '',
      translationWords: []
    });
  };

  const startEdit = (translation: TranscriptTranslation) => {
    setEditingTranslation(translation);
    setFormData({
      videoId: translation.videoId,
      languageCode: translation.languageCode,
      languageName: translation.languageName,
      translatedTranscript: translation.translatedTranscript,
      translationWords: [] // For now, we'll handle word-level translations separately
    });
    setShowCreateForm(true);
  };

  const cancelEdit = () => {
    setEditingTranslation(null);
    setShowCreateForm(false);
    resetForm();
  };

  if (loading) {
    return <div className="flex justify-center p-4">Loading translations...</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <FiGlobe className="text-blue-500" />
          Translations for "{videoTitle}"
        </h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center gap-2"
        >
          <FiPlus size={16} />
          Add Translation
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Translation List */}
      <div className="space-y-4">
        {translations.map((translation) => (
          <div key={translation.id} className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h3 className="font-medium">{translation.languageName}</h3>
                <p className="text-sm text-gray-500">Code: {translation.languageCode}</p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => startEdit(translation)}
                  className="text-blue-500 hover:text-blue-700"
                >
                  <FiEdit3 size={16} />
                </button>
                <button
                  onClick={() => handleDeleteTranslation(translation.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <FiTrash2 size={16} />
                </button>
              </div>
            </div>
            {translation.translatedTranscript && (
              <div className="mt-2">
                <p className="text-sm text-gray-600 line-clamp-3">
                  {translation.translatedTranscript}
                </p>
              </div>
            )}
            <div className="mt-2 text-xs text-gray-400">
              Created: {new Date(translation.createdAt).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>

      {translations.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No translations available. Add your first translation to get started.
        </div>
      )}

      {/* Create/Edit Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">
              {editingTranslation ? 'Edit Translation' : 'Add New Translation'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Language Code</label>
                <input
                  type="text"
                  value={formData.languageCode}
                  onChange={(e) => setFormData({ ...formData, languageCode: e.target.value })}
                  placeholder="e.g., es, fr, de"
                  className="w-full border rounded-lg px-3 py-2"
                  maxLength={10}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Language Name</label>
                <input
                  type="text"
                  value={formData.languageName}
                  onChange={(e) => setFormData({ ...formData, languageName: e.target.value })}
                  placeholder="e.g., Spanish, French, German"
                  className="w-full border rounded-lg px-3 py-2"
                  maxLength={100}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Translated Transcript</label>
                <textarea
                  value={formData.translatedTranscript}
                  onChange={(e) => setFormData({ ...formData, translatedTranscript: e.target.value })}
                  placeholder="Enter the full translated transcript..."
                  className="w-full border rounded-lg px-3 py-2 h-32 resize-none"
                />
              </div>
            </div>
            
            <div className="flex gap-2 mt-6">
              <button
                onClick={editingTranslation ? handleUpdateTranslation : handleCreateTranslation}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex-1"
                disabled={!formData.languageCode || !formData.languageName}
              >
                {editingTranslation ? 'Update' : 'Create'}
              </button>
              <button
                onClick={cancelEdit}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationManager;
