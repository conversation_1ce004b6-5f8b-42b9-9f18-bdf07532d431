@import "tailwindcss";

@layer base {
  html {
    @apply text-gray-900 dark:text-white antialiased;
  }
  
  body {
    @apply min-h-screen transition-colors duration-200;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold text-gray-900 dark:text-white;
  }
  
  p {
    @apply text-gray-600 dark:text-gray-300;
  }
  
  a {
    @apply text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors;
  }
  
  input, select, textarea, button {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 rounded;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-400 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 dark:bg-gray-800;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }
  
  .badge-secondary {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  @apply w-2 h-2;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full hover:bg-gray-500 dark:bg-gray-600 dark:hover:bg-gray-500;
}

/* Animation for page transitions */
.page-enter {
  @apply opacity-0 transform scale-95;
}

.page-enter-active {
  @apply opacity-100 scale-100 transition-all duration-200;
}

.page-exit {
  @apply opacity-100 scale-100;
}

.page-exit-active {
  @apply opacity-0 scale-95 transition-all duration-200;
}

/* Force dark mode background - explicit CSS to override any conflicts */
html:not(.dark) {
  background-color: white !important;
}

html.dark {
  background-color: rgb(17, 24, 39) !important; /* gray-900 */
}

html:not(.dark) body {
  background-color: white !important;
}

html.dark body {
  background-color: rgb(17, 24, 39) !important; /* gray-900 */
}
